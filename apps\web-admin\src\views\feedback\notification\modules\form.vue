<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createNotification, updateNotification } from '#/api';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();
const id = ref();
const isViewMode = ref(false);

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    if (isViewMode.value) {
      drawerApi.close();
      return;
    }

    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    drawerApi.lock();
    try {
      if (id.value) {
        await updateNotification(id.value, values);
        message.success('通知修改成功');
      } else {
        await createNotification(values);
        message.success('通知创建成功');
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('操作失败:', error);
      message.error($t('ui.actionMessage.operationFailed'));
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data && Object.keys(data).length > 0) {
        formData.value = data;
        id.value = data.id;
        isViewMode.value = data.viewMode || false;

        // 设置表单只读状态
        if (isViewMode.value) {
          formApi.setState({ commonConfig: { disabled: true } });
        } else {
          formApi.setState({ commonConfig: { disabled: false } });
        }

        formApi.setValues(data);
      } else {
        formData.value = {};
        id.value = undefined;
        isViewMode.value = false;
        formApi.setState({ commonConfig: { disabled: false } });
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  if (isViewMode.value) {
    return '查看通知';
  }
  return formData.value?.id
    ? '修改通知'
    : '新增通知';
});

const getConfirmText = computed(() => {
  return isViewMode.value ? '关闭' : '确定';
});
</script>

<template>
  <Drawer :title="getDrawerTitle" :confirm-text="getConfirmText">
    <Form />
  </Drawer>
</template>
