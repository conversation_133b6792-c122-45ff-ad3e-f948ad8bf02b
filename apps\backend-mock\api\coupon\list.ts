import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const couponNames = [
  '新用户专享券',
  '满减优惠券',
  '折扣券',
  '免运费券',
  '生日特惠券',
  '节日促销券',
  '会员专属券',
  '限时抢购券',
  '品类专用券',
  '满额赠券',
  '复购优惠券',
  '推荐好友券',
  '评价返现券',
  '签到奖励券',
  '积分兑换券'
];

const couponTypes = ['percentage', 'fixed', 'freeShipping'];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({ from: '2022-01-01', to: '2025-01-01' });
    const updateTime = faker.date.between({ from: createTime, to: '2025-01-01' });
    const effectiveTime = faker.date.between({ from: '2024-01-01', to: '2024-06-01' });
    const expireTime = faker.date.between({ from: effectiveTime, to: '2025-12-31' });
    const couponType = faker.helpers.arrayElement(couponTypes);
    
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      couponName: faker.helpers.arrayElement(couponNames),
      couponDescription: faker.lorem.sentences(1),
      isNewUser: faker.helpers.arrayElement([0, 1]),
      couponType: couponType,
      discountPercentage: couponType === 'percentage' ? faker.number.int({ min: 5, max: 50 }) : null,
      discountAmount: couponType === 'fixed' ? faker.number.int({ min: 5, max: 100 }) : null,
      minAmount: faker.number.int({ min: 50, max: 500 }),
      maxAmount: faker.number.int({ min: 500, max: 2000 }),
      effectiveTime: formatterCN.format(effectiveTime),
      expireTime: formatterCN.format(expireTime),
      validDays: faker.number.int({ min: 7, max: 365 }),
      status: faker.helpers.arrayElement([0, 1]),
      updateTime: formatterCN.format(updateTime),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(60);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    couponName,
    isNewUser,
    couponType,
    status,
    startTime,
    endTime,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (couponName) {
    listData = listData.filter((item) =>
      item.couponName.toLowerCase().includes(String(couponName).toLowerCase()),
    );
  }
  
  if (['0', '1'].includes(isNewUser as string)) {
    listData = listData.filter((item) => item.isNewUser === Number(isNewUser));
  }
  
  if (couponType) {
    listData = listData.filter((item) => item.couponType === couponType);
  }
  
  if (['0', '1'].includes(status as string)) {
    listData = listData.filter((item) => item.status === Number(status));
  }
  
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
