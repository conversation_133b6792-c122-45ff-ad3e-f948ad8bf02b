import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { PlanApi } from '#/api';

import { requestClient } from '#/api/request';
import { $t } from '#/locales';

// 上传函数
async function uploadFile({
  file,
  onError,
  onProgress,
  onSuccess,
}: {
  file: File;
  onError?: (error: Error) => void;
  onProgress?: (progress: { percent: number }) => void;
  onSuccess?: (data: any, file: File) => void;
}) {
  try {
    onProgress?.({ percent: 0 });
    const data = await requestClient.upload('/upload', { file });
    onProgress?.({ percent: 100 });
    onSuccess?.(data, file);
  } catch (error) {
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'planName',
      label: $t('plan.planName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'planCode',
      label: $t('plan.planCode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'productId',
      label: $t('plan.productId'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'description',
      label: $t('plan.description'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('plan.status'),
    },
    {
      component: 'Upload',
      componentProps: {
        accept: '.png,.jpg,.jpeg',
        customRequest: uploadFile,
        listType: 'picture-card',
        maxCount: 1,
        multiple: false,
        showUploadList: true,
      },
      fieldName: 'mainImage',
      label: $t('plan.mainImage'),
      help: '支持jpg、png格式，建议尺寸400x300像素',
      renderComponentContent: () => {
        return {
          default: () => '上传主图',
        };
      },
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'planName',
      label: $t('plan.planName'),
    },
    {
      component: 'Input',
      fieldName: 'planCode',
      label: $t('plan.planCode'),
    },
    {
      component: 'Input',
      fieldName: 'productId',
      label: $t('plan.productId'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('plan.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('plan.createTime'),
    },
    {
      component: 'RangePicker',
      fieldName: 'updateTime',
      label: $t('plan.updateTime'),
    },
  ];
}

export function useColumns<T = PlanApi.Plan>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('plan.id'),
      width: 80,
    },
    {
      field: 'planName',
      title: $t('plan.planName'),
      width: 150,
    },
    {
      field: 'planCode',
      title: $t('plan.planCode'),
      width: 120,
    },
    {
      field: 'productId',
      title: $t('plan.productId'),
      width: 120,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('plan.status'),
      width: 100,
    },
    {
      field: 'description',
      title: $t('plan.description'),
      minWidth: 200,
      showOverflow: true,
    },
    {
      field: 'mainImage',
      title: $t('plan.mainImage'),
      width: 100,
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: { width: '40px', height: '40px' },
        },
      },
    },
    {
      field: 'updateTime',
      title: $t('plan.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('plan.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'planName',
          nameTitle: $t('plan.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('plan.operation'),
      width: 130,
    },
  ];
}
