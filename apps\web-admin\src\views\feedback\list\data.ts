import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FeedbackApi } from '#/api';

import { $t } from '#/locales';

// 获取状态标签
function getStatusLabel(status: number): string {
  const labels: Record<number, string> = {
    0: $t('feedback.status.pending'),
    1: $t('feedback.status.processed'),
    2: $t('feedback.status.closed'),
  };
  return labels[status] || status.toString();
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'feedbackCode',
      label: $t('feedback.list.feedbackCode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('feedback.list.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('feedback.list.username'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'feedbackTitle',
      label: $t('feedback.list.feedbackTitle'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'feedbackContent',
      label: $t('feedback.list.feedbackContent'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('feedback.status.pending'), value: 0 },
          { label: $t('feedback.status.processed'), value: 1 },
          { label: $t('feedback.status.closed'), value: 2 },
        ],
      },
      defaultValue: 0,
      fieldName: 'status',
      label: $t('feedback.list.status'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'feedbackCode',
      label: $t('feedback.list.feedbackCode'),
    },
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('feedback.list.userId'),
    },
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('feedback.list.username'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('feedback.status.pending'), value: 0 },
          { label: $t('feedback.status.processed'), value: 1 },
          { label: $t('feedback.status.closed'), value: 2 },
        ],
      },
      fieldName: 'status',
      label: $t('feedback.list.status'),
    },
  ];
}

export function useColumns<T = FeedbackApi.Feedback>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('feedback.list.id'),
      width: 80,
    },
    {
      field: 'feedbackCode',
      title: $t('feedback.list.feedbackCode'),
      width: 120,
    },
    {
      field: 'userId',
      title: $t('feedback.list.userId'),
      width: 120,
    },
    {
      field: 'username',
      title: $t('feedback.list.username'),
      width: 120,
    },
    {
      field: 'feedbackTitle',
      title: $t('feedback.list.feedbackTitle'),
      width: 200,
    },
    {
      field: 'feedbackContent',
      title: $t('feedback.list.feedbackContent'),
      minWidth: 250,
      showOverflow: true,
    },
    {
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => getStatusLabel(cellValue),
        },
      },
      field: 'status',
      title: $t('feedback.list.status'),
      width: 100,
    },
    {
      field: 'createTime',
      title: $t('feedback.list.createTime'),
      width: 180,
    },
    {
      cellRender: {
        name: 'CellActions',
        props: {
          actions: [
            { code: 'view', text: '查看' },
            { code: 'edit', text: '编辑' },
          ],
          onActionClick,
        },
      },
      field: 'action',
      fixed: 'right',
      title: $t('feedback.list.operation'),
      width: 120,
    },
  ];
}
