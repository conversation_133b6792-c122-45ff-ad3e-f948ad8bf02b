import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace FeedbackApi {
  export interface Feedback {
    [key: string]: any;
    id: string;
    feedbackCode: string;
    userId: string;
    username: string;
    feedbackTitle: string;
    feedbackContent: string;
    status: 0 | 1 | 2; // 0: 待处理, 1: 已处理, 2: 已关闭
    createTime: string;
  }

  export interface Notification {
    [key: string]: any;
    id: string;
    userId: string;
    notificationType: string;
    notificationTitle: string;
    notificationContent: string;
    readStatus: 0 | 1; // 0: 未读, 1: 已读
    readTime?: string;
    updateTime: string;
    createTime: string;
  }
}

/**
 * 获取用户反馈列表数据
 */
async function getFeedbackList(params: Recordable<any>) {
  return requestClient.get<Array<FeedbackApi.Feedback>>(
    '/feedback/list',
    { params },
  );
}

/**
 * 更新用户反馈
 *
 * @param id 反馈 ID
 * @param data 反馈数据
 */
async function updateFeedback(
  id: string,
  data: Omit<FeedbackApi.Feedback, 'id' | 'createTime'>,
) {
  return requestClient.put(`/feedback/${id}`, data);
}

/**
 * 获取通知列表数据
 */
async function getNotificationList(params: Recordable<any>) {
  return requestClient.get<Array<FeedbackApi.Notification>>(
    '/notification/list',
    { params },
  );
}

/**
 * 创建通知
 * @param data 通知数据
 */
async function createNotification(data: Omit<FeedbackApi.Notification, 'id' | 'createTime' | 'updateTime'>) {
  return requestClient.post('/notification', data);
}

/**
 * 更新通知
 *
 * @param id 通知 ID
 * @param data 通知数据
 */
async function updateNotification(
  id: string,
  data: Omit<FeedbackApi.Notification, 'id' | 'createTime'>,
) {
  return requestClient.put(`/notification/${id}`, data);
}

export { createNotification, getFeedbackList, getNotificationList, updateFeedback, updateNotification };
