<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { FinanceApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteWithdrawal, getWithdrawalList } from '#/api';
import { $t } from '#/locales';

import { useWithdrawalColumns } from './columns';
import { useWithdrawalGridFormSchema } from './data';
import WithdrawalForm from './modules/withdrawal-form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: WithdrawalForm,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['requestTime', ['startTime', 'endTime']]],
    schema: useWithdrawalGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useWithdrawalColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getWithdrawalList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    exportConfig: {
      filename: '提现管理列表',
      sheetName: '提现数据',
      isHeader: true,
      isFooter: false,
      original: false,
      message: true,
      modes: ['current', 'selected'],
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<FinanceApi.Withdrawal>,
});

function onActionClick(e: OnActionClickParams<FinanceApi.Withdrawal>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

function onEdit(row: FinanceApi.Withdrawal) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: FinanceApi.Withdrawal) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.userNickname]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteWithdrawal(row.id)
    .then(() => {
      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.userNickname]),
        key: 'action_process_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('finance.withdrawalManagement')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', ['提现记录']) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
