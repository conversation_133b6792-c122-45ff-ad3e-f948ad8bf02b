{"title": "Financial Management", "rechargeManagement": "Recharge Management", "withdrawalManagement": "Withdrawal Management", "transactionRecord": "Transaction Record", "name": "Finance", "id": "ID", "userId": "User ID", "userNickname": "User Nickname", "userName": "User Name", "fbId": "FB_ID", "floatingAmount": "Floating Amount", "rechargeType": "Recharge Type", "rechargeAmount": "Recharge Amount", "rechargeMethod": "Recharge Method", "status": "Status", "virtualCoinRecovery": "Virtual Coin Recovery", "adminNote": "Admin Note", "userNote": "User Note", "updateTime": "Update Time", "createTime": "Create Time", "operation": "Operation", "withdrawalCode": "Withdrawal Code", "requestTime": "Request Time", "completeTime": "Complete Time", "transactionCode": "Transaction Code", "transactionAmount": "Transaction Amount", "transactionType": "Transaction Type", "flowDirection": "Flow Direction", "balanceBefore": "Balance Before", "balanceAfter": "Balance After", "relatedId": "Related ID", "remark": "Remark", "rechargeTypes": {"manual": "Manual Recharge", "auto": "Auto Recharge", "bonus": "Bonus Recharge", "refund": "Refund Recharge"}, "rechargeMethods": {"alipay": "Alipay", "wechat": "WeChat Pay", "bank": "Bank Transfer", "crypto": "Cryptocurrency"}, "transactionTypes": {"recharge": "Recharge", "withdrawal": "<PERSON><PERSON><PERSON>", "transfer": "Transfer", "purchase": "Purchase", "refund": "Refund", "bonus": "Bonus"}, "flowDirections": {"in": "Inflow", "out": "Outflow"}, "statusTypes": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "failed": "Failed", "cancelled": "Cancelled"}}