import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:calendar-check',
      order: 2100,
      title: $t('plan.title'),
    },
    name: 'Plan',
    path: '/plan',
    children: [
      {
        path: '/plan/list',
        name: 'PlanList',
        meta: {
          icon: 'mdi:format-list-bulleted',
          title: $t('plan.list'),
        },
        component: () => import('#/views/plan/list.vue'),
      },
    ],
  },
];

export default routes;
