import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const planNames = [
  '春季营销计划',
  '夏季促销计划',
  '秋季新品发布计划',
  '冬季清仓计划',
  '年度品牌推广计划',
  '季度销售计划',
  '月度运营计划',
  '周年庆活动计划',
  '双十一大促计划',
  '618购物节计划',
  '新用户拉新计划',
  '老用户回馈计划',
  '产品升级计划',
  '市场拓展计划',
  '渠道合作计划'
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({ from: '2022-01-01', to: '2025-01-01' });
    const updateTime = faker.date.between({ from: createTime, to: '2025-01-01' });
    
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      planName: faker.helpers.arrayElement(planNames),
      planCode: `PLAN${faker.string.numeric(6)}`,
      productId: `P${faker.string.numeric(8)}`,
      status: faker.helpers.arrayElement([0, 1]),
      description: faker.lorem.sentences(2),
      mainImage: faker.image.url({ width: 400, height: 300 }),
      updateTime: formatterCN.format(updateTime),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(80);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    planName,
    planCode,
    productId,
    status,
    startTime,
    endTime,
    updateStartTime,
    updateEndTime,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (planName) {
    listData = listData.filter((item) =>
      item.planName.toLowerCase().includes(String(planName).toLowerCase()),
    );
  }
  
  if (planCode) {
    listData = listData.filter((item) =>
      item.planCode.toLowerCase().includes(String(planCode).toLowerCase()),
    );
  }
  
  if (productId) {
    listData = listData.filter((item) =>
      item.productId.toLowerCase().includes(String(productId).toLowerCase()),
    );
  }
  
  if (['0', '1'].includes(status as string)) {
    listData = listData.filter((item) => item.status === Number(status));
  }
  
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  
  if (updateStartTime) {
    listData = listData.filter((item) => item.updateTime >= updateStartTime);
  }
  
  if (updateEndTime) {
    listData = listData.filter((item) => item.updateTime <= updateEndTime);
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
