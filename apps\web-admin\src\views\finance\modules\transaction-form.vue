<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createTransaction, updateTransaction } from '#/api';
import { $t } from '#/locales';

import { useTransactionFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();
const id = ref();

const [Form, formApi] = useVbenForm({
  schema: useTransactionFormSchema(),
  showDefaultActions: false,
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    
    const values = await formApi.getValues();
    
    drawerApi.lock();
    try {
      if (id.value) {
        await updateTransaction(id.value, values);
        message.success(`交易记录修改成功`);
      } else {
        await createTransaction(values);
        message.success(`交易记录创建成功`);
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('操作失败:', error);
      message.error($t('ui.actionMessage.operationFailed'));
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data && Object.keys(data).length > 0) {
        formData.value = data;
        id.value = data.id;
        formApi.setValues(data);
      } else {
        formData.value = {};
        id.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? `修改交易记录`
    : `新增交易记录`;
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
