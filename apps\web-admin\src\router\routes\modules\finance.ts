import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:cash-multiple',
      order: 2400,
      title: $t('finance.title'),
    },
    name: 'Finance',
    path: '/finance',
    children: [
      {
        path: '/finance/recharge',
        name: 'FinanceRecharge',
        meta: {
          icon: 'mdi:cash-plus',
          title: $t('finance.rechargeManagement'),
        },
        component: () => import('#/views/finance/recharge-list.vue'),
      },
      {
        path: '/finance/withdrawal',
        name: 'FinanceWithdrawal',
        meta: {
          icon: 'mdi:cash-minus',
          title: $t('finance.withdrawalManagement'),
        },
        component: () => import('#/views/finance/withdrawal-list.vue'),
      },
      {
        path: '/finance/transaction',
        name: 'FinanceTransaction',
        meta: {
          icon: 'mdi:swap-horizontal',
          title: $t('finance.transactionRecord'),
        },
        component: () => import('#/views/finance/transaction-list.vue'),
      },
    ],
  },
];

export default routes;
