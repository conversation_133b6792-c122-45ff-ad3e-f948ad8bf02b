import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { FinanceApi } from '#/api';

import { $t } from '#/locales';

// 获取充值类型显示名称
function getRechargeTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    manual: $t('finance.rechargeTypes.manual'),
    auto: $t('finance.rechargeTypes.auto'),
    bonus: $t('finance.rechargeTypes.bonus'),
    refund: $t('finance.rechargeTypes.refund'),
  };
  return typeMap[type] || type;
}

// 获取充值方式显示名称
function getRechargeMethodLabel(method: string): string {
  const methodMap: Record<string, string> = {
    alipay: $t('finance.rechargeMethods.alipay'),
    wechat: $t('finance.rechargeMethods.wechat'),
    bank: $t('finance.rechargeMethods.bank'),
    crypto: $t('finance.rechargeMethods.crypto'),
  };
  return methodMap[method] || method;
}

// 获取交易类型显示名称
function getTransactionTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    recharge: $t('finance.transactionTypes.recharge'),
    withdrawal: $t('finance.transactionTypes.withdrawal'),
    transfer: $t('finance.transactionTypes.transfer'),
    purchase: $t('finance.transactionTypes.purchase'),
    refund: $t('finance.transactionTypes.refund'),
    bonus: $t('finance.transactionTypes.bonus'),
  };
  return typeMap[type] || type;
}

// 获取流动方向显示名称
function getFlowDirectionLabel(direction: string): string {
  const directionMap: Record<string, string> = {
    in: $t('finance.flowDirections.in'),
    out: $t('finance.flowDirections.out'),
  };
  return directionMap[direction] || direction;
}

// 获取状态显示名称
function getStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    pending: $t('finance.statusTypes.pending'),
    processing: $t('finance.statusTypes.processing'),
    completed: $t('finance.statusTypes.completed'),
    failed: $t('finance.statusTypes.failed'),
    cancelled: $t('finance.statusTypes.cancelled'),
  };
  return statusMap[status] || status;
}

// 充值管理表单配置
export function useRechargeFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('finance.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('finance.userNickname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'fbId',
      label: $t('finance.fbId'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入浮动金额',
      },
      fieldName: 'floatingAmount',
      label: $t('finance.floatingAmount'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.rechargeTypes.manual'), value: 'manual' },
          { label: $t('finance.rechargeTypes.auto'), value: 'auto' },
          { label: $t('finance.rechargeTypes.bonus'), value: 'bonus' },
          { label: $t('finance.rechargeTypes.refund'), value: 'refund' },
        ],
      },
      fieldName: 'rechargeType',
      label: $t('finance.rechargeType'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入充值数额',
      },
      fieldName: 'rechargeAmount',
      label: $t('finance.rechargeAmount'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.rechargeMethods.alipay'), value: 'alipay' },
          { label: $t('finance.rechargeMethods.wechat'), value: 'wechat' },
          { label: $t('finance.rechargeMethods.bank'), value: 'bank' },
          { label: $t('finance.rechargeMethods.crypto'), value: 'crypto' },
        ],
      },
      fieldName: 'rechargeMethod',
      label: $t('finance.rechargeMethod'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.statusTypes.pending'), value: 'pending' },
          { label: $t('finance.statusTypes.processing'), value: 'processing' },
          { label: $t('finance.statusTypes.completed'), value: 'completed' },
          { label: $t('finance.statusTypes.failed'), value: 'failed' },
          { label: $t('finance.statusTypes.cancelled'), value: 'cancelled' },
        ],
      },
      fieldName: 'status',
      label: $t('finance.status'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入虚拟币充值回收',
      },
      fieldName: 'virtualCoinRecovery',
      label: $t('finance.virtualCoinRecovery'),
    },
    {
      component: 'Textarea',
      fieldName: 'adminNote',
      label: $t('finance.adminNote'),
    },
    {
      component: 'Textarea',
      fieldName: 'userNote',
      label: $t('finance.userNote'),
    },
  ];
}

// 充值管理搜索表单配置
export function useRechargeGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('finance.userId'),
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('finance.userNickname'),
    },
    {
      component: 'Input',
      fieldName: 'fbId',
      label: $t('finance.fbId'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.rechargeTypes.manual'), value: 'manual' },
          { label: $t('finance.rechargeTypes.auto'), value: 'auto' },
          { label: $t('finance.rechargeTypes.bonus'), value: 'bonus' },
          { label: $t('finance.rechargeTypes.refund'), value: 'refund' },
        ],
      },
      fieldName: 'rechargeType',
      label: $t('finance.rechargeType'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.rechargeMethods.alipay'), value: 'alipay' },
          { label: $t('finance.rechargeMethods.wechat'), value: 'wechat' },
          { label: $t('finance.rechargeMethods.bank'), value: 'bank' },
          { label: $t('finance.rechargeMethods.crypto'), value: 'crypto' },
        ],
      },
      fieldName: 'rechargeMethod',
      label: $t('finance.rechargeMethod'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.statusTypes.pending'), value: 'pending' },
          { label: $t('finance.statusTypes.processing'), value: 'processing' },
          { label: $t('finance.statusTypes.completed'), value: 'completed' },
          { label: $t('finance.statusTypes.failed'), value: 'failed' },
          { label: $t('finance.statusTypes.cancelled'), value: 'cancelled' },
        ],
      },
      fieldName: 'status',
      label: $t('finance.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('finance.createTime'),
    },
  ];
}

// 提现管理表单配置
export function useWithdrawalFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('finance.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('finance.userNickname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'withdrawalCode',
      label: $t('finance.withdrawalCode'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入浮动金额',
      },
      fieldName: 'floatingAmount',
      label: $t('finance.floatingAmount'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.statusTypes.pending'), value: 'pending' },
          { label: $t('finance.statusTypes.processing'), value: 'processing' },
          { label: $t('finance.statusTypes.completed'), value: 'completed' },
          { label: $t('finance.statusTypes.failed'), value: 'failed' },
          { label: $t('finance.statusTypes.cancelled'), value: 'cancelled' },
        ],
      },
      fieldName: 'status',
      label: $t('finance.status'),
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择请求时间',
      },
      fieldName: 'requestTime',
      label: $t('finance.requestTime'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择完成时间',
      },
      fieldName: 'completeTime',
      label: $t('finance.completeTime'),
    },
  ];
}

// 提现管理搜索表单配置
export function useWithdrawalGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('finance.userId'),
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('finance.userNickname'),
    },
    {
      component: 'Input',
      fieldName: 'withdrawalCode',
      label: $t('finance.withdrawalCode'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.statusTypes.pending'), value: 'pending' },
          { label: $t('finance.statusTypes.processing'), value: 'processing' },
          { label: $t('finance.statusTypes.completed'), value: 'completed' },
          { label: $t('finance.statusTypes.failed'), value: 'failed' },
          { label: $t('finance.statusTypes.cancelled'), value: 'cancelled' },
        ],
      },
      fieldName: 'status',
      label: $t('finance.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'requestTime',
      label: $t('finance.requestTime'),
    },
  ];
}

// 交易记录表单配置
export function useTransactionFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('finance.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userName',
      label: $t('finance.userName'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'transactionCode',
      label: $t('finance.transactionCode'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入交易金额',
      },
      fieldName: 'transactionAmount',
      label: $t('finance.transactionAmount'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.transactionTypes.recharge'), value: 'recharge' },
          { label: $t('finance.transactionTypes.withdrawal'), value: 'withdrawal' },
          { label: $t('finance.transactionTypes.transfer'), value: 'transfer' },
          { label: $t('finance.transactionTypes.purchase'), value: 'purchase' },
          { label: $t('finance.transactionTypes.refund'), value: 'refund' },
          { label: $t('finance.transactionTypes.bonus'), value: 'bonus' },
        ],
      },
      fieldName: 'transactionType',
      label: $t('finance.transactionType'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.flowDirections.in'), value: 'in' },
          { label: $t('finance.flowDirections.out'), value: 'out' },
        ],
      },
      fieldName: 'flowDirection',
      label: $t('finance.flowDirection'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入变化前余额',
      },
      fieldName: 'balanceBefore',
      label: $t('finance.balanceBefore'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入变化后余额',
      },
      fieldName: 'balanceAfter',
      label: $t('finance.balanceAfter'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'relatedId',
      label: $t('finance.relatedId'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.statusTypes.pending'), value: 'pending' },
          { label: $t('finance.statusTypes.processing'), value: 'processing' },
          { label: $t('finance.statusTypes.completed'), value: 'completed' },
          { label: $t('finance.statusTypes.failed'), value: 'failed' },
          { label: $t('finance.statusTypes.cancelled'), value: 'cancelled' },
        ],
      },
      fieldName: 'status',
      label: $t('finance.status'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: $t('finance.remark'),
    },
  ];
}

// 交易记录搜索表单配置
export function useTransactionGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('finance.userId'),
    },
    {
      component: 'Input',
      fieldName: 'userName',
      label: $t('finance.userName'),
    },
    {
      component: 'Input',
      fieldName: 'transactionCode',
      label: $t('finance.transactionCode'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.transactionTypes.recharge'), value: 'recharge' },
          { label: $t('finance.transactionTypes.withdrawal'), value: 'withdrawal' },
          { label: $t('finance.transactionTypes.transfer'), value: 'transfer' },
          { label: $t('finance.transactionTypes.purchase'), value: 'purchase' },
          { label: $t('finance.transactionTypes.refund'), value: 'refund' },
          { label: $t('finance.transactionTypes.bonus'), value: 'bonus' },
        ],
      },
      fieldName: 'transactionType',
      label: $t('finance.transactionType'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.flowDirections.in'), value: 'in' },
          { label: $t('finance.flowDirections.out'), value: 'out' },
        ],
      },
      fieldName: 'flowDirection',
      label: $t('finance.flowDirection'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('finance.statusTypes.pending'), value: 'pending' },
          { label: $t('finance.statusTypes.processing'), value: 'processing' },
          { label: $t('finance.statusTypes.completed'), value: 'completed' },
          { label: $t('finance.statusTypes.failed'), value: 'failed' },
          { label: $t('finance.statusTypes.cancelled'), value: 'cancelled' },
        ],
      },
      fieldName: 'status',
      label: $t('finance.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('finance.createTime'),
    },
  ];
}
