import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const userNames = [
  '张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十',
  '郑一', '王二', '冯三', '陈四', '褚五', '卫六', '蒋七', '沈八',
  '韩九', '杨十', '朱一', '秦二', '尤三', '许四', '何五', '吕六',
  '施七', '张八', '孔九', '曹十', '严一', '华二', '金三', '魏四'
];

const transactionTypes = ['recharge', 'withdrawal', 'transfer', 'purchase', 'refund', 'bonus'];
const flowDirections = ['in', 'out'];
const statusTypes = ['pending', 'processing', 'completed', 'failed', 'cancelled'];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({ from: '2022-01-01', to: '2025-01-01' });
    const transactionAmount = faker.number.float({ min: 1, max: 5000, fractionDigits: 2 });
    const flowDirection = faker.helpers.arrayElement(flowDirections);
    const balanceBefore = faker.number.float({ min: 0, max: 10000, fractionDigits: 2 });
    const balanceAfter = flowDirection === 'in' 
      ? balanceBefore + transactionAmount 
      : Math.max(0, balanceBefore - transactionAmount);
    
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      userId: `U${faker.string.numeric(8)}`,
      userName: faker.helpers.arrayElement(userNames),
      transactionCode: `TX${faker.string.numeric(16)}`,
      transactionAmount: transactionAmount,
      transactionType: faker.helpers.arrayElement(transactionTypes),
      flowDirection: flowDirection,
      balanceBefore: balanceBefore,
      balanceAfter: balanceAfter,
      relatedId: faker.helpers.maybe(() => `R${faker.string.numeric(8)}`, { probability: 0.6 }),
      status: faker.helpers.arrayElement(statusTypes),
      remark: faker.helpers.maybe(() => faker.lorem.sentence(), { probability: 0.4 }),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(100);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    userId,
    userName,
    transactionCode,
    transactionType,
    flowDirection,
    status,
    startTime,
    endTime,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }
  
  if (userName) {
    listData = listData.filter((item) =>
      item.userName.toLowerCase().includes(String(userName).toLowerCase()),
    );
  }
  
  if (transactionCode) {
    listData = listData.filter((item) =>
      item.transactionCode.toLowerCase().includes(String(transactionCode).toLowerCase()),
    );
  }
  
  if (transactionType) {
    listData = listData.filter((item) => item.transactionType === transactionType);
  }
  
  if (flowDirection) {
    listData = listData.filter((item) => item.flowDirection === flowDirection);
  }
  
  if (status) {
    listData = listData.filter((item) => item.status === status);
  }
  
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
