import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:ticket-percent',
      order: 2200,
      title: $t('coupon.title'),
    },
    name: 'Coupon',
    path: '/coupon',
    children: [
      {
        path: '/coupon/list',
        name: 'CouponList',
        meta: {
          icon: 'mdi:format-list-bulleted',
          title: $t('coupon.list'),
        },
        component: () => import('#/views/coupon/list.vue'),
      },
      {
        path: '/coupon/user-coupon-list',
        name: 'UserCouponList',
        meta: {
          icon: 'mdi:account-multiple',
          title: $t('coupon.userCouponList'),
        },
        component: () => import('#/views/coupon/user-coupon-list.vue'),
      },
    ],
  },
];

export default routes;
