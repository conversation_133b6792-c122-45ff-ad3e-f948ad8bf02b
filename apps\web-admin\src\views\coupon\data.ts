import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CouponApi } from '#/api';

import { $t } from '#/locales';

// 获取券类型显示名称
function getCouponTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    percentage: $t('coupon.type.percentage'),
    fixed: $t('coupon.type.fixed'),
    freeShipping: $t('coupon.type.freeShipping'),
  };
  return typeMap[type] || type;
}

// 获取用户优惠券状态显示名称
function getUserCouponStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    unused: $t('coupon.userCouponStatus.unused'),
    used: $t('coupon.userCouponStatus.used'),
    expired: $t('coupon.userCouponStatus.expired'),
  };
  return statusMap[status] || status;
}

export function useCouponFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'couponName',
      label: $t('coupon.couponName'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'couponDescription',
      label: $t('coupon.couponDescription'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 0,
      fieldName: 'isNewUser',
      label: $t('coupon.isNewUser'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('coupon.type.percentage'), value: 'percentage' },
          { label: $t('coupon.type.fixed'), value: 'fixed' },
          { label: $t('coupon.type.freeShipping'), value: 'freeShipping' },
        ],
      },
      fieldName: 'couponType',
      label: $t('coupon.couponType'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        max: 100,
        precision: 2,
        placeholder: '请输入折扣百分比（0-100）',
      },
      fieldName: 'discountPercentage',
      label: $t('coupon.discountPercentage'),
      dependencies: {
        triggerFields: ['couponType'],
        show: (values) => values.couponType === 'percentage',
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入折扣金额',
      },
      fieldName: 'discountAmount',
      label: $t('coupon.discountAmount'),
      dependencies: {
        triggerFields: ['couponType'],
        show: (values) => values.couponType === 'fixed',
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入最低使用金额',
      },
      fieldName: 'minAmount',
      label: $t('coupon.minAmount'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入最高使用金额',
      },
      fieldName: 'maxAmount',
      label: $t('coupon.maxAmount'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择生效时间',
      },
      fieldName: 'effectiveTime',
      label: $t('coupon.effectiveTime'),
    },
    {
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择过期时间',
      },
      fieldName: 'expireTime',
      label: $t('coupon.expireTime'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入有效天数',
      },
      fieldName: 'validDays',
      label: $t('coupon.validDays'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('coupon.status'),
    },
  ];
}

export function useCouponGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'couponName',
      label: $t('coupon.couponName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 },
        ],
      },
      fieldName: 'isNewUser',
      label: $t('coupon.isNewUser'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('coupon.type.percentage'), value: 'percentage' },
          { label: $t('coupon.type.fixed'), value: 'fixed' },
          { label: $t('coupon.type.freeShipping'), value: 'freeShipping' },
        ],
      },
      fieldName: 'couponType',
      label: $t('coupon.couponType'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('coupon.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('coupon.createTime'),
    },
  ];
}

export function useUserCouponFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('coupon.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('coupon.userNickname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'couponId',
      label: $t('coupon.couponId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'fbId',
      label: $t('coupon.fbId'),
    },
    {
      component: 'Input',
      fieldName: 'couponName',
      label: $t('coupon.couponName'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('coupon.type.percentage'), value: 'percentage' },
          { label: $t('coupon.type.fixed'), value: 'fixed' },
          { label: $t('coupon.type.freeShipping'), value: 'freeShipping' },
        ],
      },
      fieldName: 'couponType',
      label: $t('coupon.couponTypeName'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('coupon.userCouponStatus.unused'), value: 'unused' },
          { label: $t('coupon.userCouponStatus.used'), value: 'used' },
          { label: $t('coupon.userCouponStatus.expired'), value: 'expired' },
        ],
      },
      fieldName: 'status',
      label: $t('coupon.status'),
      rules: 'required',
    },
    {
      component: 'DatePicker',
      componentProps: {
        showTime: true,
        format: 'YYYY-MM-DD HH:mm:ss',
        placeholder: '请选择过期时间',
      },
      fieldName: 'expireTime',
      label: $t('coupon.expireTime'),
    },
  ];
}

export function useUserCouponGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('coupon.userId'),
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('coupon.userNickname'),
    },
    {
      component: 'Input',
      fieldName: 'couponId',
      label: $t('coupon.couponId'),
    },
    {
      component: 'Input',
      fieldName: 'fbId',
      label: $t('coupon.fbId'),
    },
    {
      component: 'Input',
      fieldName: 'couponName',
      label: $t('coupon.couponName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('coupon.type.percentage'), value: 'percentage' },
          { label: $t('coupon.type.fixed'), value: 'fixed' },
          { label: $t('coupon.type.freeShipping'), value: 'freeShipping' },
        ],
      },
      fieldName: 'couponType',
      label: $t('coupon.couponTypeName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('coupon.userCouponStatus.unused'), value: 'unused' },
          { label: $t('coupon.userCouponStatus.used'), value: 'used' },
          { label: $t('coupon.userCouponStatus.expired'), value: 'expired' },
        ],
      },
      fieldName: 'status',
      label: $t('coupon.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('coupon.createTime'),
    },
  ];
}

export function useCouponColumns<T = CouponApi.Coupon>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('coupon.id'),
      width: 80,
    },
    {
      field: 'couponName',
      title: $t('coupon.couponName'),
      width: 150,
    },
    {
      field: 'couponDescription',
      title: $t('coupon.couponDescription'),
      minWidth: 200,
      showOverflow: true,
    },
    {
      field: 'isNewUser',
      title: $t('coupon.isNewUser'),
      width: 100,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => cellValue === 1 ? '是' : '否',
        },
      },
    },
    {
      field: 'couponType',
      title: $t('coupon.couponType'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getCouponTypeLabel(cellValue),
        },
      },
    },
    {
      field: 'discountPercentage',
      title: $t('coupon.discountPercentage'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => cellValue ? `${cellValue}%` : '-',
        },
      },
    },
    {
      field: 'discountAmount',
      title: $t('coupon.discountAmount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => cellValue ? `¥${cellValue}` : '-',
        },
      },
    },
    {
      field: 'minAmount',
      title: $t('coupon.minAmount'),
      width: 140,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => cellValue ? `¥${cellValue}` : '-',
        },
      },
    },
    {
      field: 'maxAmount',
      title: $t('coupon.maxAmount'),
      width: 140,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => cellValue ? `¥${cellValue}` : '-',
        },
      },
    },
    {
      field: 'effectiveTime',
      title: $t('coupon.effectiveTime'),
      width: 180,
    },
    {
      field: 'expireTime',
      title: $t('coupon.expireTime'),
      width: 180,
    },
    {
      field: 'validDays',
      title: $t('coupon.validDays'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => cellValue ? `${cellValue}天` : '-',
        },
      },
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('coupon.status'),
      width: 100,
    },
    {
      field: 'updateTime',
      title: $t('coupon.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('coupon.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'couponName',
          nameTitle: $t('coupon.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('coupon.operation'),
      width: 130,
    },
  ];
}

export function useUserCouponColumns<T = CouponApi.UserCoupon>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('coupon.id'),
      width: 80,
    },
    {
      field: 'userId',
      title: $t('coupon.userId'),
      width: 120,
    },
    {
      field: 'userNickname',
      title: $t('coupon.userNickname'),
      width: 150,
    },
    {
      field: 'couponId',
      title: $t('coupon.couponId'),
      width: 120,
    },
    {
      field: 'fbId',
      title: $t('coupon.fbId'),
      width: 120,
    },
    {
      field: 'couponName',
      title: $t('coupon.couponName'),
      width: 150,
    },
    {
      field: 'couponType',
      title: $t('coupon.couponTypeName'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getCouponTypeLabel(cellValue),
        },
      },
    },
    {
      field: 'status',
      title: $t('coupon.status'),
      width: 100,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getUserCouponStatusLabel(cellValue),
        },
      },
    },
    {
      field: 'expireTime',
      title: $t('coupon.expireTime'),
      width: 180,
    },
    {
      field: 'updateTime',
      title: $t('coupon.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('coupon.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'couponName',
          nameTitle: $t('coupon.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('coupon.operation'),
      width: 130,
    },
  ];
}
