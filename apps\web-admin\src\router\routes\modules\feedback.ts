import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:message-text',
      order: 2600,
      title: $t('feedback.title'),
    },
    name: 'Feedback',
    path: '/feedback',
    children: [
      {
        path: '/feedback/list',
        name: 'FeedbackList',
        meta: {
          icon: 'mdi:comment-text-multiple',
          title: $t('feedback.list.title'),
        },
        component: () => import('#/views/feedback/list/list.vue'),
      },
      {
        path: '/feedback/notification',
        name: 'FeedbackNotification',
        meta: {
          icon: 'mdi:bell',
          title: $t('feedback.notification.title'),
        },
        component: () => import('#/views/feedback/notification/list.vue'),
      },
    ],
  },
];

export default routes;
