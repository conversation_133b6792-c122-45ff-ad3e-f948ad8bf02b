import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProductApi } from '#/api';

import { requestClient } from '#/api/request';
import { $t } from '#/locales';

// 上传函数
async function uploadFile({
  file,
  onError,
  onProgress,
  onSuccess,
}: {
  file: File;
  onError?: (error: Error) => void;
  onProgress?: (progress: { percent: number }) => void;
  onSuccess?: (data: any, file: File) => void;
}) {
  try {
    onProgress?.({ percent: 0 });
    const data = await requestClient.upload('/upload', { file });
    onProgress?.({ percent: 100 });
    onSuccess?.(data, file);
  } catch (error) {
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'productCode',
      label: $t('product.productCode'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: $t('product.productName'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('product.category.electronics'), value: 'electronics' },
          { label: $t('product.category.clothing'), value: 'clothing' },
          { label: $t('product.category.food'), value: 'food' },
          { label: $t('product.category.books'), value: 'books' },
          { label: $t('product.category.home'), value: 'home' },
          { label: $t('product.category.sports'), value: 'sports' },
          { label: $t('product.category.beauty'), value: 'beauty' },
          { label: $t('product.category.automotive'), value: 'automotive' },
        ],
      },
      fieldName: 'productCategory',
      label: $t('product.productCategory'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: $t('product.companyName'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'productDescription',
      label: $t('product.productDescription'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('product.status'),
    },
    {
      component: 'Upload',
      componentProps: {
        accept: '.png,.jpg,.jpeg',
        customRequest: uploadFile,
        listType: 'picture-card',
        maxCount: 1,
        multiple: false,
        showUploadList: true,
      },
      fieldName: 'productLogo',
      label: $t('product.productLogo'),
      help: '支持jpg、png格式，建议尺寸100x100像素',
      renderComponentContent: () => {
        return {
          default: () => '上传Logo',
        };
      },
    },
    {
      component: 'Upload',
      componentProps: {
        accept: '.png,.jpg,.jpeg',
        customRequest: uploadFile,
        listType: 'picture-card',
        maxCount: 1,
        multiple: false,
        showUploadList: true,
      },
      fieldName: 'productMainImage',
      label: $t('product.productMainImage'),
      help: '支持jpg、png格式，建议尺寸400x300像素',
      renderComponentContent: () => {
        return {
          default: () => '上传主图',
        };
      },
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'productCode',
      label: $t('product.productCode'),
    },
    {
      component: 'Input',
      fieldName: 'productName',
      label: $t('product.productName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('product.category.electronics'), value: 'electronics' },
          { label: $t('product.category.clothing'), value: 'clothing' },
          { label: $t('product.category.food'), value: 'food' },
          { label: $t('product.category.books'), value: 'books' },
          { label: $t('product.category.home'), value: 'home' },
          { label: $t('product.category.sports'), value: 'sports' },
          { label: $t('product.category.beauty'), value: 'beauty' },
          { label: $t('product.category.automotive'), value: 'automotive' },
        ],
      },
      fieldName: 'productCategory',
      label: $t('product.productCategory'),
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: $t('product.companyName'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('product.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('product.createTime'),
    },
  ];
}

// 获取分类显示名称
function getCategoryLabel(category: string): string {
  const categoryMap: Record<string, string> = {
    electronics: $t('product.category.electronics'),
    clothing: $t('product.category.clothing'),
    food: $t('product.category.food'),
    books: $t('product.category.books'),
    home: $t('product.category.home'),
    sports: $t('product.category.sports'),
    beauty: $t('product.category.beauty'),
    automotive: $t('product.category.automotive'),
  };
  return categoryMap[category] || category;
}

export function useColumns<T = ProductApi.Product>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('product.id'),
      width: 80,
    },
    {
      field: 'productCode',
      title: $t('product.productCode'),
      width: 120,
    },
    {
      field: 'productName',
      title: $t('product.productName'),
      width: 150,
    },
    {
      field: 'productCategory',
      title: $t('product.productCategory'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getCategoryLabel(cellValue),
        },
      },
    },
    {
      field: 'companyName',
      title: $t('product.companyName'),
      width: 150,
    },
    {
      field: 'productDescription',
      title: $t('product.productDescription'),
      minWidth: 200,
      showOverflow: true,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('product.status'),
      width: 100,
    },
    {
      field: 'productLogo',
      title: $t('product.productLogo'),
      width: 100,
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: { width: '40px', height: '40px' },
        },
      },
    },
    {
      field: 'productMainImage',
      title: $t('product.productMainImage'),
      width: 100,
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: { width: '40px', height: '40px' },
        },
      },
    },
    {
      field: 'createTime',
      title: $t('product.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'productName',
          nameTitle: $t('product.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('product.operation'),
      width: 130,
    },
  ];
}
