import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:crown',
      order: 2400,
      title: $t('vip.title'),
    },
    name: 'Vip',
    path: '/vip',
    children: [
      {
        path: '/vip/level',
        name: 'VipLevel',
        meta: {
          icon: 'mdi:star-circle',
          title: $t('vip.level.title'),
        },
        component: () => import('#/views/vip/level/list.vue'),
      },
    ],
  },
];

export default routes;
