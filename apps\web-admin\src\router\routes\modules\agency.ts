import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:account-group',
      order: 2300,
      title: $t('agency.title'),
    },
    name: 'Agency',
    path: '/agency',
    children: [
      {
        path: '/agency/list',
        name: 'AgencyList',
        meta: {
          icon: 'mdi:format-list-bulleted',
          title: $t('agency.list'),
        },
        component: () => import('#/views/agency/list.vue'),
      },
    ],
  },
];

export default routes;
