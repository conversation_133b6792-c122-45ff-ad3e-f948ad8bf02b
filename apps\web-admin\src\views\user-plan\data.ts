import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UserPlanApi } from '#/api';

import { $t } from '#/locales';

// 获取投放状态显示名称
function getLaunchStatusLabel(status: string): string {
  const statusMap: Record<string, string> = {
    pending: $t('userPlan.launchStatusTypes.pending'),
    launching: $t('userPlan.launchStatusTypes.launching'),
    completed: $t('userPlan.launchStatusTypes.completed'),
    paused: $t('userPlan.launchStatusTypes.paused'),
    failed: $t('userPlan.launchStatusTypes.failed'),
  };
  return statusMap[status] || status;
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'groupPlanId',
      label: $t('userPlan.groupPlanId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('userPlan.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('userPlan.userNickname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'orderNumber',
      label: $t('userPlan.orderNumber'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        placeholder: '请输入组内顺序',
      },
      fieldName: 'groupOrder',
      label: $t('userPlan.groupOrder'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入投放金额',
      },
      fieldName: 'launchAmount',
      label: $t('userPlan.launchAmount'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        max: 100,
        precision: 2,
        placeholder: '请输入投放进度（0-100）',
      },
      fieldName: 'launchProgress',
      label: $t('userPlan.launchProgress'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入已投放金额',
      },
      fieldName: 'launched',
      label: $t('userPlan.launched'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        placeholder: '请输入待投放金额',
      },
      fieldName: 'pending',
      label: $t('userPlan.pending'),
      rules: 'required',
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('userPlan.launchStatusTypes.pending'), value: 'pending' },
          { label: $t('userPlan.launchStatusTypes.launching'), value: 'launching' },
          { label: $t('userPlan.launchStatusTypes.completed'), value: 'completed' },
          { label: $t('userPlan.launchStatusTypes.paused'), value: 'paused' },
          { label: $t('userPlan.launchStatusTypes.failed'), value: 'failed' },
        ],
      },
      fieldName: 'launchStatus',
      label: $t('userPlan.launchStatus'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 4,
        placeholder: '请输入点击单价',
      },
      fieldName: 'clickPrice',
      label: $t('userPlan.clickPrice'),
      rules: 'required',
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'groupPlanId',
      label: $t('userPlan.groupPlanId'),
    },
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('userPlan.userId'),
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('userPlan.userNickname'),
    },
    {
      component: 'Input',
      fieldName: 'orderNumber',
      label: $t('userPlan.orderNumber'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('userPlan.launchStatusTypes.pending'), value: 'pending' },
          { label: $t('userPlan.launchStatusTypes.launching'), value: 'launching' },
          { label: $t('userPlan.launchStatusTypes.completed'), value: 'completed' },
          { label: $t('userPlan.launchStatusTypes.paused'), value: 'paused' },
          { label: $t('userPlan.launchStatusTypes.failed'), value: 'failed' },
        ],
      },
      fieldName: 'launchStatus',
      label: $t('userPlan.launchStatus'),
    },
    {
      component: 'RangePicker',
      fieldName: 'updateTime',
      label: $t('userPlan.updateTime'),
    },
  ];
}

export function useColumns<T = UserPlanApi.UserPlan>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'groupPlanId',
      title: $t('userPlan.groupPlanId'),
      width: 150,
    },
    {
      field: 'userId',
      title: $t('userPlan.userId'),
      width: 120,
    },
    {
      field: 'userNickname',
      title: $t('userPlan.userNickname'),
      width: 150,
    },
    {
      field: 'orderNumber',
      title: $t('userPlan.orderNumber'),
      width: 150,
    },
    {
      field: 'groupOrder',
      title: $t('userPlan.groupOrder'),
      width: 100,
    },
    {
      field: 'launchAmount',
      title: $t('userPlan.launchAmount'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'launchProgress',
      title: $t('userPlan.launchProgress'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `${cellValue}%`,
        },
      },
    },
    {
      field: 'launched',
      title: $t('userPlan.launched'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'pending',
      title: $t('userPlan.pending'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'launchStatus',
      title: $t('userPlan.launchStatus'),
      width: 120,
      cellRender: {
        name: 'CellTag',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getLaunchStatusLabel(cellValue),
        },
      },
    },
    {
      field: 'clickPrice',
      title: $t('userPlan.clickPrice'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => `¥${cellValue}`,
        },
      },
    },
    {
      field: 'updateTime',
      title: $t('userPlan.updateTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'userNickname',
          nameTitle: $t('userPlan.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('userPlan.operation'),
      width: 130,
    },
  ];
}
