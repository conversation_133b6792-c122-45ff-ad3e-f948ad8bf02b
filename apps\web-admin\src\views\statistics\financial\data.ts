import type { VbenFormSchema } from '#/adapter/form';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { $t } from '#/locales';

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'DatePicker',
      componentProps: {
        placeholder: '请选择日期',
        style: { width: '100%' },
      },
      fieldName: 'date',
      label: $t('statistics.financial.date'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入充值金额',
        style: { width: '100%' },
        min: 0,
        precision: 2,
      },
      fieldName: 'rechargeAmount',
      label: $t('statistics.financial.rechargeAmount'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        placeholder: '请输入提现金额',
        style: { width: '100%' },
        min: 0,
        precision: 2,
      },
      fieldName: 'withdrawAmount',
      label: $t('statistics.financial.withdrawAmount'),
    },
  ];
}

export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'date',
      title: $t('statistics.financial.date'),
      width: 120,
    },
    {
      field: 'rechargeAmount',
      title: $t('statistics.financial.rechargeAmount'),
      width: 150,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `¥${cellValue.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}` : '¥0.00',
        },
      },
    },
    {
      field: 'withdrawAmount',
      title: $t('statistics.financial.withdrawAmount'),
      width: 150,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `¥${cellValue.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}` : '¥0.00',
        },
      },
    },
    {
      field: 'updateTime',
      title: $t('statistics.financial.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('statistics.financial.createTime'),
      width: 180,
    },
  ];
}
