import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const userNicknames = [
  '小明', '小红', '小李', '小王', '小张', '小刘', '小陈', '小杨', '小赵', '小孙',
  '阿强', '阿华', '阿伟', '阿军', '阿峰', '阿涛', '阿斌', '阿超', '阿龙', '阿飞',
  '晓雯', '晓敏', '晓丽', '晓燕', '晓霞', '晓琳', '晓娟', '晓芳', '晓慧', '晓玲'
];

const statusTypes = ['pending', 'processing', 'completed', 'failed', 'cancelled'];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const requestTime = faker.date.between({ from: '2022-01-01', to: '2025-01-01' });
    const completeTime = faker.helpers.maybe(() => 
      faker.date.between({ from: requestTime, to: '2025-01-01' }), 
      { probability: 0.7 }
    );
    
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      userId: `U${faker.string.numeric(8)}`,
      userNickname: faker.helpers.arrayElement(userNicknames),
      withdrawalCode: `WD${faker.string.numeric(12)}`,
      floatingAmount: faker.number.float({ min: 50, max: 8000, fractionDigits: 2 }),
      status: faker.helpers.arrayElement(statusTypes),
      requestTime: formatterCN.format(requestTime),
      completeTime: completeTime ? formatterCN.format(completeTime) : null,
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(60);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    userId,
    userNickname,
    withdrawalCode,
    status,
    startTime,
    endTime,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }
  
  if (userNickname) {
    listData = listData.filter((item) =>
      item.userNickname.toLowerCase().includes(String(userNickname).toLowerCase()),
    );
  }
  
  if (withdrawalCode) {
    listData = listData.filter((item) =>
      item.withdrawalCode.toLowerCase().includes(String(withdrawalCode).toLowerCase()),
    );
  }
  
  if (status) {
    listData = listData.filter((item) => item.status === status);
  }
  
  if (startTime) {
    listData = listData.filter((item) => item.requestTime >= startTime);
  }
  
  if (endTime) {
    listData = listData.filter((item) => item.requestTime <= endTime);
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
