<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { FinanceApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteRecharge, getRechargeList } from '#/api';
import { $t } from '#/locales';

import { useRechargeColumns } from './columns';
import { useRechargeGridFormSchema } from './data';
import RechargeForm from './modules/recharge-form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: RechargeForm,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useRechargeGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useRechargeColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getRechargeList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    exportConfig: {
      filename: '充值管理列表',
      sheetName: '充值数据',
      isHeader: true,
      isFooter: false,
      original: false,
      message: true,
      modes: ['current', 'selected'],
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<FinanceApi.Recharge>,
});

function onActionClick(e: OnActionClickParams<FinanceApi.Recharge>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

function onEdit(row: FinanceApi.Recharge) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: FinanceApi.Recharge) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.userNickname]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteRecharge(row.id)
    .then(() => {
      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.userNickname]),
        key: 'action_process_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('finance.rechargeManagement')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', ['充值记录']) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
