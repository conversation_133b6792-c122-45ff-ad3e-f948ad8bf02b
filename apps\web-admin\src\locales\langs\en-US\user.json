{"title": "User Management", "name": "User", "list": {"title": "User List Management", "userId": "User ID", "username": "Username", "nickname": "Nickname", "avatar": "Avatar", "adminId": "Admin ID", "vipLevel": "VIP Level", "parentUserId": "Parent User ID", "agencyRelation": "Agency Relation", "languagePreference": "Language Preference", "facebookId": "Facebook ID", "authCode": "Auth Code", "withdrawMethod": "Withdraw Method", "availableBalance": "Available Balance", "status": "Status", "userPerspective": "User Perspective", "createTime": "Create Time", "updateTime": "Update Time", "operation": "Operation"}, "agencyRelation": {"direct": "Direct Agency", "indirect": "Indirect Agency", "none": "No Agency"}, "languagePreference": {"zh-CN": "Simplified Chinese", "en-US": "English", "zh-TW": "Traditional Chinese"}, "withdrawMethod": {"bank": "Bank Card", "alipay": "Alipay", "wechat": "WeChat", "paypal": "PayPal"}, "userPerspective": {"customer": "Customer Perspective", "agent": "Agent Perspective", "admin": "Admin Perspective"}}