import { faker } from '@faker-js/faker';

import { defineFakeRoute } from '@vben/backend-mock/fake-route';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false,
});

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const date = faker.date.between({ from: '2024-01-01', to: '2024-12-31' });
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      date: date.toISOString().split('T')[0], // YYYY-MM-DD format
      registrationCount: faker.number.int({ min: 10, max: 500 }),
      updateTime: formatterCN.format(
        faker.date.between({ from: date, to: new Date() }),
      ),
      createTime: formatterCN.format(date),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

export default defineFakeRoute([
  {
    url: '/api/statistics/registration',
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10 } = query;

      const list = generateMockDataList(Number(pageSize));

      return {
        code: 0,
        data: {
          items: list,
          total: 100,
        },
        message: 'ok',
      };
    },
  },
]);
