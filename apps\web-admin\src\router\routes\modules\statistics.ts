import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:chart-line',
      order: 3000,
      title: $t('statistics.title'),
    },
    name: 'Statistics',
    path: '/statistics',
    children: [
      {
        path: '/statistics/registration',
        name: 'StatisticsRegistration',
        meta: {
          icon: 'mdi:account-plus',
          title: $t('statistics.registration.title'),
        },
        component: () => import('#/views/statistics/registration/list.vue'),
      },
      {
        path: '/statistics/financial',
        name: 'StatisticsFinancial',
        meta: {
          icon: 'mdi:currency-usd',
          title: $t('statistics.financial.title'),
        },
        component: () => import('#/views/statistics/financial/list.vue'),
      },
    ],
  },
];

export default routes;
