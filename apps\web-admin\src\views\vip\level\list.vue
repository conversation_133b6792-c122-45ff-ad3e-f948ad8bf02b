<script lang="ts" setup>


import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { VipApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteVipLevel, getVipLevelList } from '#/api';
import { $t } from '#/locales';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: Form,
  destroyOnClose: true,
});

/**
 * 表格操作按钮的回调函数
 */
function onActionClick({
  code,
  row,
}: OnActionClickParams<VipApi.VipLevel>) {
  switch (code) {
    case 'delete': {
      onDelete(row);
      break;
    }
    case 'edit': {
      onEdit(row);
      break;
    }
  }
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getVipLevelList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    exportConfig: {
      filename: 'VIP等级列表',
      sheetName: 'VIP等级数据',
      isHeader: true,
      isFooter: false,
      original: false,
      message: true,
      modes: ['current', 'selected'],
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<VipApi.VipLevel>,
});

/**
 * 刷新表格
 */
function onRefresh() {
  gridApi.query();
}

/**
 * 新增
 */
function onCreate() {
  formDrawerApi.setData({}).open();
}

/**
 * 编辑
 */
function onEdit(row: VipApi.VipLevel) {
  formDrawerApi.setData(row).open();
}

/**
 * 删除
 */
function onDelete(row: VipApi.VipLevel) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除VIP等级"${row.levelName}"吗？`,
    onOk: async () => {
      try {
        await deleteVipLevel(row.id);
        message.success('删除成功');
        onRefresh();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
}
</script>

<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('vip.level.title')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', [$t('vip.name')]) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
