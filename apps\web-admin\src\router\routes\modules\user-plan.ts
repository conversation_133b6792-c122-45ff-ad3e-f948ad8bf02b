import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:account-clock',
      order: 2500,
      title: $t('userPlan.title'),
    },
    name: 'UserPlan',
    path: '/user-plan',
    children: [
      {
        path: '/user-plan/list',
        name: 'UserPlanList',
        meta: {
          icon: 'mdi:format-list-bulleted',
          title: $t('userPlan.list'),
        },
        component: () => import('#/views/user-plan/list.vue'),
      },
    ],
  },
];

export default routes;
