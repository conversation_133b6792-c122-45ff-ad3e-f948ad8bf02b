import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const agencyRelations = ['direct', 'indirect', 'none'];
const languagePreferences = ['zh-CN', 'en-US', 'zh-TW'];
const withdrawMethods = ['bank', 'alipay', 'wechat', 'paypal'];
const userPerspectives = ['customer', 'agent', 'admin'];

const adminIds = [
  'admin001', 'admin002', 'admin003', 'admin004', 'admin005'
];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const dataItem: Record<string, any> = {
      userId: `U${faker.string.numeric(8)}`,
      username: faker.internet.userName(),
      nickname: faker.person.fullName(),
      avatar: faker.image.avatar(),
      adminId: faker.helpers.arrayElement(adminIds),
      vipLevel: faker.number.int({ min: 0, max: 8 }),
      parentUserId: faker.datatype.boolean() ? `U${faker.string.numeric(8)}` : null,
      agencyRelation: faker.helpers.arrayElement(agencyRelations),
      languagePreference: faker.helpers.arrayElement(languagePreferences),
      facebookId: faker.datatype.boolean() ? faker.string.numeric(15) : null,
      authCode: faker.string.alphanumeric(12).toUpperCase(),
      withdrawMethod: faker.helpers.arrayElement(withdrawMethods),
      availableBalance: Number(faker.finance.amount({ min: 0, max: 50000, dec: 2 })),
      status: faker.helpers.arrayElement([0, 1]),
      userPerspective: faker.helpers.arrayElement(userPerspectives),
      createTime: formatterCN.format(
        faker.date.between({ from: '2022-01-01', to: '2025-01-01' }),
      ),
      updateTime: formatterCN.format(
        faker.date.between({ from: '2023-01-01', to: '2025-01-01' }),
      ),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(200);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    username,
    nickname,
    agencyRelation,
    vipLevel,
    startTime,
    endTime,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (username) {
    listData = listData.filter((item) =>
      item.username.toLowerCase().includes(String(username).toLowerCase()),
    );
  }
  
  if (nickname) {
    listData = listData.filter((item) =>
      item.nickname.toLowerCase().includes(String(nickname).toLowerCase()),
    );
  }
  
  if (agencyRelation) {
    listData = listData.filter((item) => item.agencyRelation === agencyRelation);
  }
  
  if (vipLevel) {
    listData = listData.filter((item) => item.vipLevel === Number(vipLevel));
  }
  
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
