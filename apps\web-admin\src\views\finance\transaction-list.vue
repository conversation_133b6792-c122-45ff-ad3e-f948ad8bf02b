<script lang="ts" setup>
import type {
  OnActionClickParams,
  VxeTableGridOptions,
} from '#/adapter/vxe-table';
import type { FinanceApi } from '#/api';

import { Page, useVbenDrawer } from '@vben/common-ui';
import { Plus } from '@vben/icons';

import { Button, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { deleteTransaction, getTransactionList } from '#/api';
import { $t } from '#/locales';

import { useTransactionColumns } from './columns';
import { useTransactionGridFormSchema } from './data';
import TransactionForm from './modules/transaction-form.vue';

const [FormDrawer, formDrawerApi] = useVbenDrawer({
  connectedComponent: TransactionForm,
  destroyOnClose: true,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    fieldMappingTime: [['createTime', ['startTime', 'endTime']]],
    schema: useTransactionGridFormSchema(),
    submitOnChange: true,
  },
  gridOptions: {
    columns: useTransactionColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }, formValues) => {
          return await getTransactionList({
            page: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
        },
      },
    },
    rowConfig: {
      keyField: 'id',
    },
    exportConfig: {
      filename: '交易记录列表',
      sheetName: '交易数据',
      isHeader: true,
      isFooter: false,
      original: false,
      message: true,
      modes: ['current', 'selected'],
    },
    toolbarConfig: {
      custom: true,
      export: true,
      refresh: { code: 'query' },
      search: true,
      zoom: true,
    },
  } as VxeTableGridOptions<FinanceApi.Transaction>,
});

function onActionClick(e: OnActionClickParams<FinanceApi.Transaction>) {
  switch (e.code) {
    case 'delete': {
      onDelete(e.row);
      break;
    }
    case 'edit': {
      onEdit(e.row);
      break;
    }
  }
}

function onEdit(row: FinanceApi.Transaction) {
  formDrawerApi.setData(row).open();
}

function onDelete(row: FinanceApi.Transaction) {
  const hideLoading = message.loading({
    content: $t('ui.actionMessage.deleting', [row.userName]),
    duration: 0,
    key: 'action_process_msg',
  });
  deleteTransaction(row.id)
    .then(() => {
      message.success({
        content: $t('ui.actionMessage.deleteSuccess', [row.userName]),
        key: 'action_process_msg',
      });
      onRefresh();
    })
    .catch(() => {
      hideLoading();
    });
}

function onRefresh() {
  gridApi.query();
}

function onCreate() {
  formDrawerApi.setData({}).open();
}
</script>
<template>
  <Page auto-content-height>
    <FormDrawer @success="onRefresh" />
    <Grid :table-title="$t('finance.transactionRecord')">
      <template #toolbar-tools>
        <Button type="primary" @click="onCreate">
          <Plus class="size-5" />
          {{ $t('ui.actionTitle.create', ['交易记录']) }}
        </Button>
      </template>
    </Grid>
  </Page>
</template>
