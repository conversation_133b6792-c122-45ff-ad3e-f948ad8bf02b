import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { UserApi } from '#/api';

import { $t } from '#/locales';

// 获取代理关系标签
function getAgencyRelationLabel(relation: string): string {
  const labels: Record<string, string> = {
    direct: $t('user.agencyRelation.direct'),
    indirect: $t('user.agencyRelation.indirect'),
    none: $t('user.agencyRelation.none'),
  };
  return labels[relation] || relation;
}

// 获取语言偏好标签
function getLanguagePreferenceLabel(language: string): string {
  const labels: Record<string, string> = {
    'zh-CN': $t('user.languagePreference.zh-CN'),
    'en-US': $t('user.languagePreference.en-US'),
    'zh-TW': $t('user.languagePreference.zh-TW'),
  };
  return labels[language] || language;
}

// 获取提现方式标签
function getWithdrawMethodLabel(method: string): string {
  const labels: Record<string, string> = {
    bank: $t('user.withdrawMethod.bank'),
    alipay: $t('user.withdrawMethod.alipay'),
    wechat: $t('user.withdrawMethod.wechat'),
    paypal: $t('user.withdrawMethod.paypal'),
  };
  return labels[method] || method;
}

// 获取用户视角标签
function getUserPerspectiveLabel(perspective: string): string {
  const labels: Record<string, string> = {
    customer: $t('user.userPerspective.customer'),
    agent: $t('user.userPerspective.agent'),
    admin: $t('user.userPerspective.admin'),
  };
  return labels[perspective] || perspective;
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('user.list.username'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'nickname',
      label: $t('user.list.nickname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'adminId',
      label: $t('user.list.adminId'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        max: 10,
        style: { width: '100%' },
      },
      fieldName: 'vipLevel',
      label: $t('user.list.vipLevel'),
    },
    {
      component: 'Input',
      fieldName: 'parentUserId',
      label: $t('user.list.parentUserId'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('user.agencyRelation.direct'), value: 'direct' },
          { label: $t('user.agencyRelation.indirect'), value: 'indirect' },
          { label: $t('user.agencyRelation.none'), value: 'none' },
        ],
      },
      fieldName: 'agencyRelation',
      label: $t('user.list.agencyRelation'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('user.languagePreference.zh-CN'), value: 'zh-CN' },
          { label: $t('user.languagePreference.en-US'), value: 'en-US' },
          { label: $t('user.languagePreference.zh-TW'), value: 'zh-TW' },
        ],
      },
      fieldName: 'languagePreference',
      label: $t('user.list.languagePreference'),
    },
    {
      component: 'Input',
      fieldName: 'facebookId',
      label: $t('user.list.facebookId'),
    },
    {
      component: 'Input',
      fieldName: 'authCode',
      label: $t('user.list.authCode'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('user.withdrawMethod.bank'), value: 'bank' },
          { label: $t('user.withdrawMethod.alipay'), value: 'alipay' },
          { label: $t('user.withdrawMethod.wechat'), value: 'wechat' },
          { label: $t('user.withdrawMethod.paypal'), value: 'paypal' },
        ],
      },
      fieldName: 'withdrawMethod',
      label: $t('user.list.withdrawMethod'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        style: { width: '100%' },
        addonAfter: '元',
      },
      fieldName: 'availableBalance',
      label: $t('user.list.availableBalance'),
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('user.list.status'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('user.userPerspective.customer'), value: 'customer' },
          { label: $t('user.userPerspective.agent'), value: 'agent' },
          { label: $t('user.userPerspective.admin'), value: 'admin' },
        ],
      },
      fieldName: 'userPerspective',
      label: $t('user.list.userPerspective'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'username',
      label: $t('user.list.username'),
    },
    {
      component: 'Input',
      fieldName: 'nickname',
      label: $t('user.list.nickname'),
    },
    {
      component: 'Select',
      componentProps: {
        options: [
          { label: $t('user.agencyRelation.direct'), value: 'direct' },
          { label: $t('user.agencyRelation.indirect'), value: 'indirect' },
          { label: $t('user.agencyRelation.none'), value: 'none' },
        ],
      },
      fieldName: 'agencyRelation',
      label: $t('user.list.agencyRelation'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        max: 10,
        style: { width: '100%' },
      },
      fieldName: 'vipLevel',
      label: $t('user.list.vipLevel'),
    },
  ];
}

export function useColumns<T = UserApi.User>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'userId',
      title: $t('user.list.userId'),
      width: 120,
    },
    {
      field: 'username',
      title: $t('user.list.username'),
      width: 120,
    },
    {
      field: 'nickname',
      title: $t('user.list.nickname'),
      width: 120,
    },
    {
      field: 'avatar',
      title: $t('user.list.avatar'),
      width: 80,
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: { width: '40px', height: '40px', borderRadius: '50%' },
        },
      },
    },
    {
      field: 'adminId',
      title: $t('user.list.adminId'),
      width: 120,
    },
    {
      field: 'vipLevel',
      title: $t('user.list.vipLevel'),
      width: 100,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `VIP${cellValue}` : 'VIP0',
        },
      },
    },
    {
      field: 'parentUserId',
      title: $t('user.list.parentUserId'),
      width: 120,
    },
    {
      field: 'agencyRelation',
      title: $t('user.list.agencyRelation'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getAgencyRelationLabel(cellValue),
        },
      },
    },
    {
      field: 'languagePreference',
      title: $t('user.list.languagePreference'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getLanguagePreferenceLabel(cellValue),
        },
      },
    },
    {
      field: 'facebookId',
      title: $t('user.list.facebookId'),
      width: 120,
    },
    {
      field: 'authCode',
      title: $t('user.list.authCode'),
      width: 120,
    },
    {
      field: 'withdrawMethod',
      title: $t('user.list.withdrawMethod'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getWithdrawMethodLabel(cellValue),
        },
      },
    },
    {
      field: 'availableBalance',
      title: $t('user.list.availableBalance'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `¥${cellValue.toFixed(2)}` : '¥0.00',
        },
      },
    },
    {
      cellRender: {
        name: 'CellTag',
      },
      field: 'status',
      title: $t('user.list.status'),
      width: 100,
    },
    {
      field: 'userPerspective',
      title: $t('user.list.userPerspective'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: string }) => getUserPerspectiveLabel(cellValue),
        },
      },
    },
    {
      field: 'createTime',
      title: $t('user.list.createTime'),
      width: 180,
    },
    {
      field: 'updateTime',
      title: $t('user.list.updateTime'),
      width: 180,
    },
    {
      cellRender: {
        name: 'CellActions',
        props: {
          actions: [
            { code: 'view', text: '查看' },
            { code: 'edit', text: '编辑' },
          ],
          onActionClick,
        },
      },
      field: 'action',
      fixed: 'right',
      title: $t('user.list.operation'),
      width: 120,
    },
  ];
}
