<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { updateFeedback } from '#/api';
import { $t } from '#/locales';

import { useFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();
const id = ref();
const isViewMode = ref(false);

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    if (isViewMode.value) {
      drawerApi.close();
      return;
    }

    const { valid } = await formApi.validate();
    if (!valid) return;

    const values = await formApi.getValues();

    drawerApi.lock();
    try {
      if (id.value) {
        await updateFeedback(id.value, values);
        message.success(`${$t('feedback.name')}修改成功`);
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('操作失败:', error);
      message.error($t('ui.actionMessage.operationFailed'));
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data && Object.keys(data).length > 0) {
        formData.value = data;
        id.value = data.id;
        isViewMode.value = data.viewMode || false;

        // 设置表单只读状态
        if (isViewMode.value) {
          formApi.setFieldsDisabled(true);
        } else {
          formApi.setFieldsDisabled(false);
        }

        formApi.setValues(data);
      } else {
        formData.value = {};
        id.value = undefined;
        isViewMode.value = false;
        formApi.setFieldsDisabled(false);
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  if (isViewMode.value) {
    return `查看${$t('feedback.name')}`;
  }
  return formData.value?.id
    ? `修改${$t('feedback.name')}`
    : `新增${$t('feedback.name')}`;
});

const getConfirmText = computed(() => {
  return isViewMode.value ? '关闭' : '确定';
});
</script>

<template>
  <Drawer :title="getDrawerTitle" :confirm-text="getConfirmText">
    <Form />
  </Drawer>
</template>
