import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const id = getRouterParam(event, 'id');
  
  // 模拟删除提现记录
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 500));

  return useResponseSuccess({ id, message: '删除成功' });
});
