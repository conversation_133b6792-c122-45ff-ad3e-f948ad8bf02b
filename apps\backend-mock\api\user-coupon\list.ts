import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, usePageResponseSuccess } from '~/utils/response';

const formatterCN = new Intl.DateTimeFormat('zh-CN', {
  timeZone: 'Asia/Shanghai',
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
});

const couponNames = [
  '新用户专享券',
  '满减优惠券',
  '折扣券',
  '免运费券',
  '生日特惠券',
  '节日促销券',
  '会员专属券',
  '限时抢购券',
  '品类专用券',
  '满额赠券'
];

const userNicknames = [
  '小明', '小红', '小李', '小王', '小张', '小刘', '小陈', '小杨', '小赵', '小孙',
  '阿强', '阿华', '阿伟', '阿军', '阿峰', '阿涛', '阿斌', '阿超', '阿龙', '阿飞'
];

const couponTypes = ['percentage', 'fixed', 'freeShipping'];
const userCouponStatuses = ['unused', 'used', 'expired'];

function generateMockDataList(count: number) {
  const dataList = [];

  for (let i = 0; i < count; i++) {
    const createTime = faker.date.between({ from: '2022-01-01', to: '2025-01-01' });
    const updateTime = faker.date.between({ from: createTime, to: '2025-01-01' });
    const expireTime = faker.date.between({ from: createTime, to: '2025-12-31' });
    
    const dataItem: Record<string, any> = {
      id: faker.string.uuid(),
      userId: `U${faker.string.numeric(8)}`,
      userNickname: faker.helpers.arrayElement(userNicknames),
      couponId: `C${faker.string.numeric(8)}`,
      fbId: `FB${faker.string.numeric(10)}`,
      couponName: faker.helpers.arrayElement(couponNames),
      couponType: faker.helpers.arrayElement(couponTypes),
      status: faker.helpers.arrayElement(userCouponStatuses),
      expireTime: formatterCN.format(expireTime),
      updateTime: formatterCN.format(updateTime),
      createTime: formatterCN.format(createTime),
    };

    dataList.push(dataItem);
  }

  return dataList;
}

const mockData = generateMockDataList(80);

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  const {
    page = 1,
    pageSize = 20,
    userId,
    userNickname,
    couponId,
    fbId,
    couponName,
    couponType,
    status,
    startTime,
    endTime,
  } = getQuery(event);
  
  let listData = structuredClone(mockData);
  
  if (userId) {
    listData = listData.filter((item) =>
      item.userId.toLowerCase().includes(String(userId).toLowerCase()),
    );
  }
  
  if (userNickname) {
    listData = listData.filter((item) =>
      item.userNickname.toLowerCase().includes(String(userNickname).toLowerCase()),
    );
  }
  
  if (couponId) {
    listData = listData.filter((item) =>
      item.couponId.toLowerCase().includes(String(couponId).toLowerCase()),
    );
  }
  
  if (fbId) {
    listData = listData.filter((item) =>
      item.fbId.toLowerCase().includes(String(fbId).toLowerCase()),
    );
  }
  
  if (couponName) {
    listData = listData.filter((item) =>
      item.couponName.toLowerCase().includes(String(couponName).toLowerCase()),
    );
  }
  
  if (couponType) {
    listData = listData.filter((item) => item.couponType === couponType);
  }
  
  if (status) {
    listData = listData.filter((item) => item.status === status);
  }
  
  if (startTime) {
    listData = listData.filter((item) => item.createTime >= startTime);
  }
  
  if (endTime) {
    listData = listData.filter((item) => item.createTime <= endTime);
  }
  
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
