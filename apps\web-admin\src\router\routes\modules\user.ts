import type { RouteRecordRaw } from 'vue-router';

import { $t } from '#/locales';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'mdi:account-group',
      order: 2500,
      title: $t('user.title'),
    },
    name: 'User',
    path: '/user',
    children: [
      {
        path: '/user/list',
        name: 'UserList',
        meta: {
          icon: 'mdi:account-multiple',
          title: $t('user.list.title'),
        },
        component: () => import('#/views/user/list/list.vue'),
      },
    ],
  },
];

export default routes;
