import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace UserApi {
  export interface User {
    [key: string]: any;
    userId: string;
    username: string;
    nickname: string;
    avatar?: string;
    adminId: string;
    vipLevel: number;
    parentUserId?: string;
    agencyRelation: string;
    languagePreference: string;
    facebookId?: string;
    authCode: string;
    withdrawMethod: string;
    availableBalance: number;
    status: 0 | 1;
    userPerspective: string;
    createTime?: string;
    updateTime?: string;
  }
}

/**
 * 获取用户列表数据
 */
async function getUserList(params: Recordable<any>) {
  return requestClient.get<Array<UserApi.User>>(
    '/user/list',
    { params },
  );
}

/**
 * 更新用户
 *
 * @param id 用户 ID
 * @param data 用户数据
 */
async function updateUser(
  id: string,
  data: Omit<UserApi.User, 'userId' | 'createTime' | 'updateTime'>,
) {
  return requestClient.put(`/user/${id}`, data);
}

/**
 * 获取用户详情
 * @param id 用户 ID
 */
async function getUserDetail(id: string) {
  return requestClient.get<UserApi.User>(`/user/${id}`);
}

export { getUserDetail, getUserList, updateUser };
