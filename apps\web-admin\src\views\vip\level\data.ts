import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { VipApi } from '#/api';

import { requestClient } from '#/api/request';
import { $t } from '#/locales';

// 上传函数
async function uploadFile({
  file,
  onError,
  onProgress,
  onSuccess,
}: {
  file: File;
  onError?: (error: Error) => void;
  onProgress?: (progress: { percent: number }) => void;
  onSuccess?: (data: any, file: File) => void;
}) {
  try {
    onProgress?.({ percent: 0 });
    const data = await requestClient.upload('/upload', { file });
    onProgress?.({ percent: 100 });
    onSuccess?.(data, file);
  } catch (error) {
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Upload',
      componentProps: {
        accept: '.png,.jpg,.jpeg',
        customRequest: uploadFile,
        listType: 'picture-card',
        maxCount: 1,
        multiple: false,
        showUploadList: true,
      },
      fieldName: 'icon',
      label: $t('vip.level.icon'),
      help: '支持jpg、png格式，建议尺寸64x64像素',
      renderComponentContent: () => {
        return {
          default: () => '上传图标',
        };
      },
    },
    {
      component: 'Input',
      fieldName: 'levelName',
      label: $t('vip.level.levelName'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        max: 999,
        style: { width: '100%' },
      },
      fieldName: 'levelValue',
      label: $t('vip.level.levelValue'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        style: { width: '100%' },
        addonAfter: '元',
      },
      fieldName: 'clickPrice',
      label: $t('vip.level.clickPrice'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'commissionDetail',
      label: $t('vip.level.commissionDetail'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        precision: 2,
        style: { width: '100%' },
        addonAfter: '元',
      },
      fieldName: 'purchasePrice',
      label: $t('vip.level.purchasePrice'),
      rules: 'required',
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 0,
        style: { width: '100%' },
        addonAfter: '条',
      },
      fieldName: 'dataLimit',
      label: $t('vip.level.dataLimit'),
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'vipPrivileges',
      label: $t('vip.level.vipPrivileges'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'levelName',
      label: $t('vip.level.levelName'),
    },
    {
      component: 'InputNumber',
      componentProps: {
        min: 1,
        max: 999,
        style: { width: '100%' },
      },
      fieldName: 'levelValue',
      label: $t('vip.level.levelValue'),
    },
  ];
}

export function useColumns<T = VipApi.VipLevel>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('vip.level.id'),
      width: 80,
    },
    {
      field: 'icon',
      title: $t('vip.level.icon'),
      width: 80,
      cellRender: {
        name: 'CellImage',
        attrs: {
          style: { width: '40px', height: '40px' },
        },
      },
    },
    {
      field: 'levelName',
      title: $t('vip.level.levelName'),
      width: 120,
    },
    {
      field: 'levelValue',
      title: $t('vip.level.levelValue'),
      width: 100,
    },
    {
      field: 'clickPrice',
      title: $t('vip.level.clickPrice'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `¥${cellValue.toFixed(2)}` : '¥0.00',
        },
      },
    },
    {
      field: 'commissionDetail',
      title: $t('vip.level.commissionDetail'),
      minWidth: 150,
      showOverflow: true,
    },
    {
      field: 'purchasePrice',
      title: $t('vip.level.purchasePrice'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `¥${cellValue.toFixed(2)}` : '¥0.00',
        },
      },
    },
    {
      field: 'dataLimit',
      title: $t('vip.level.dataLimit'),
      width: 120,
      cellRender: {
        name: 'CellText',
        attrs: {
          formatter: ({ cellValue }: { cellValue: number }) => 
            cellValue ? `${cellValue}条` : '0条',
        },
      },
    },
    {
      field: 'vipPrivileges',
      title: $t('vip.level.vipPrivileges'),
      minWidth: 200,
      showOverflow: true,
    },
    {
      field: 'createTime',
      title: $t('vip.level.createTime'),
      width: 180,
    },
    {
      field: 'updateTime',
      title: $t('vip.level.updateTime'),
      width: 180,
    },
    {
      cellRender: {
        name: 'CellActions',
        props: {
          actions: [
            { code: 'edit', text: '编辑' },
            { code: 'delete', text: '删除' },
          ],
          onActionClick,
        },
      },
      field: 'action',
      fixed: 'right',
      title: $t('vip.level.operation'),
      width: 120,
    },
  ];
}
