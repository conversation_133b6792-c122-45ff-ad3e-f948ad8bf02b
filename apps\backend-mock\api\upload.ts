import { faker } from '@faker-js/faker';
import { verifyAccessToken } from '~/utils/jwt-utils';
import { unAuthorizedResponse, useResponseSuccess } from '~/utils/response';

const sampleImages = [
  'https://unpkg.com/@vbenjs/static-source@0.1.7/source/logo-v1.webp',
  'https://picsum.photos/400/300?random=1',
  'https://picsum.photos/400/300?random=2',
  'https://picsum.photos/400/300?random=3',
  'https://picsum.photos/400/300?random=4',
  'https://picsum.photos/400/300?random=5',
];

export default eventHandler(async (event) => {
  const userinfo = verifyAccessToken(event);
  if (!userinfo) {
    return unAuthorizedResponse(event);
  }

  // 模拟上传延迟
  await new Promise(resolve => setTimeout(resolve, 1000));

  // 随机返回一个图片URL
  const randomImage = faker.helpers.arrayElement(sampleImages);

  return useResponseSuccess({
    url: randomImage,
    name: 'uploaded-image.jpg',
    size: faker.number.int({ min: 10000, max: 500000 }),
  });
});
