import type { VbenFormSchema } from '#/adapter/form';
import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AgencyApi } from '#/api';

import { $t } from '#/locales';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('agency.userId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('agency.userNickname'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'agencyId',
      label: $t('agency.agencyId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'followedUserId',
      label: $t('agency.followedUserId'),
      rules: 'required',
    },
    {
      component: 'Input',
      fieldName: 'followedAgencyId',
      label: $t('agency.followedAgencyId'),
      rules: 'required',
    },
    {
      component: 'RadioGroup',
      componentProps: {
        buttonStyle: 'solid',
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
        optionType: 'button',
      },
      defaultValue: 1,
      fieldName: 'status',
      label: $t('agency.status'),
    },
  ];
}

export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'userId',
      label: $t('agency.userId'),
    },
    {
      component: 'Input',
      fieldName: 'userNickname',
      label: $t('agency.userNickname'),
    },
    {
      component: 'Input',
      fieldName: 'agencyId',
      label: $t('agency.agencyId'),
    },
    {
      component: 'Input',
      fieldName: 'followedUserId',
      label: $t('agency.followedUserId'),
    },
    {
      component: 'Input',
      fieldName: 'followedAgencyId',
      label: $t('agency.followedAgencyId'),
    },
    {
      component: 'Select',
      componentProps: {
        allowClear: true,
        options: [
          { label: $t('common.enabled'), value: 1 },
          { label: $t('common.disabled'), value: 0 },
        ],
      },
      fieldName: 'status',
      label: $t('agency.status'),
    },
    {
      component: 'RangePicker',
      fieldName: 'createTime',
      label: $t('agency.createTime'),
    },
    {
      component: 'RangePicker',
      fieldName: 'updateTime',
      label: $t('agency.updateTime'),
    },
  ];
}

export function useColumns<T = AgencyApi.Agency>(
  onActionClick: OnActionClickFn<T>,
  onStatusChange?: (newStatus: any, row: T) => PromiseLike<boolean | undefined>,
): VxeTableGridOptions['columns'] {
  return [
    {
      field: 'id',
      title: $t('agency.id'),
      width: 80,
    },
    {
      field: 'userId',
      title: $t('agency.userId'),
      width: 120,
    },
    {
      field: 'userNickname',
      title: $t('agency.userNickname'),
      width: 150,
    },
    {
      field: 'agencyId',
      title: $t('agency.agencyId'),
      width: 120,
    },
    {
      field: 'followedUserId',
      title: $t('agency.followedUserId'),
      width: 150,
    },
    {
      field: 'followedAgencyId',
      title: $t('agency.followedAgencyId'),
      width: 150,
    },
    {
      cellRender: {
        attrs: { beforeChange: onStatusChange },
        name: onStatusChange ? 'CellSwitch' : 'CellTag',
      },
      field: 'status',
      title: $t('agency.status'),
      width: 100,
    },
    {
      field: 'updateTime',
      title: $t('agency.updateTime'),
      width: 180,
    },
    {
      field: 'createTime',
      title: $t('agency.createTime'),
      width: 180,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'userNickname',
          nameTitle: $t('agency.name'),
          onClick: onActionClick,
        },
        name: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      title: $t('agency.operation'),
      width: 130,
    },
  ];
}
