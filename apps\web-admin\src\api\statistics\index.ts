import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace StatisticsApi {
  export interface RegistrationStatistics {
    [key: string]: any;
    id: string;
    date: string;
    registrationCount: number;
    updateTime: string;
    createTime: string;
  }

  export interface FinancialStatistics {
    [key: string]: any;
    id: string;
    date: string;
    rechargeAmount: number;
    withdrawAmount: number;
    updateTime: string;
    createTime: string;
  }
}

/**
 * 获取注册统计数据
 */
async function getRegistrationStatistics(params: Recordable<any>) {
  return requestClient.get<Array<StatisticsApi.RegistrationStatistics>>(
    '/statistics/registration',
    { params },
  );
}

/**
 * 获取财务统计数据
 */
async function getFinancialStatistics(params: Recordable<any>) {
  return requestClient.get<Array<StatisticsApi.FinancialStatistics>>(
    '/statistics/financial',
    { params },
  );
}

export { getRegistrationStatistics, getFinancialStatistics };
