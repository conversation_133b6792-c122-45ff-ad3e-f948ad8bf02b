import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace PlanApi {
  export interface Plan {
    [key: string]: any;
    id: string;
    planName: string;
    planCode: string;
    productId: string;
    status: 0 | 1;
    description?: string;
    mainImage?: string;
    updateTime?: string;
    createTime?: string;
  }
}

/**
 * 获取计划列表数据
 */
async function getPlanList(params: Recordable<any>) {
  return requestClient.get<Array<PlanApi.Plan>>(
    '/plan/list',
    { params },
  );
}

/**
 * 创建计划
 * @param data 计划数据
 */
async function createPlan(data: Omit<PlanApi.Plan, 'id' | 'createTime' | 'updateTime'>) {
  return requestClient.post('/plan', data);
}

/**
 * 更新计划
 *
 * @param id 计划 ID
 * @param data 计划数据
 */
async function updatePlan(
  id: string,
  data: Omit<PlanApi.Plan, 'id' | 'createTime' | 'updateTime'>,
) {
  return requestClient.put(`/plan/${id}`, data);
}

/**
 * 删除计划
 * @param id 计划 ID
 */
async function deletePlan(id: string) {
  return requestClient.delete(`/plan/${id}`);
}

export { createPlan, deletePlan, getPlanList, updatePlan };
