{"title": "User <PERSON>", "name": "<PERSON><PERSON><PERSON>", "list": {"title": "User Feedback List", "id": "ID", "feedbackCode": "Feedback Code", "userId": "User ID", "username": "Username", "feedbackTitle": "Feedback Title", "feedbackContent": "Feedback Content", "status": "Status", "createTime": "Create Time", "operation": "Operation"}, "notification": {"title": "Notification Management", "id": "ID", "userId": "User ID", "notificationType": "Notification Type", "notificationTitle": "Notification Title", "notificationContent": "Notification Content", "readStatus": "Read Status", "readTime": "Read Time", "updateTime": "Update Time", "createTime": "Create Time", "operation": "Operation"}, "status": {"pending": "Pending", "processed": "Processed", "closed": "Closed"}, "readStatus": {"unread": "Unread", "read": "Read"}, "notificationType": {"system": "System Notification", "promotion": "Promotion Notification", "reminder": "Reminder Notification", "warning": "Warning Notification"}}