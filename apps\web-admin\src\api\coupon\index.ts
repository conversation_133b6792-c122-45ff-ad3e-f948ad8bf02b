import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

export namespace CouponApi {
  export interface Coupon {
    [key: string]: any;
    id: string;
    couponName: string;
    couponDescription?: string;
    isNewUser: 0 | 1;
    couponType: 'percentage' | 'fixed' | 'freeShipping';
    discountPercentage?: number;
    discountAmount?: number;
    minAmount?: number;
    maxAmount?: number;
    effectiveTime?: string;
    expireTime?: string;
    validDays?: number;
    status: 0 | 1;
    updateTime?: string;
    createTime?: string;
  }

  export interface UserCoupon {
    [key: string]: any;
    id: string;
    userId: string;
    userNickname: string;
    couponId: string;
    fbId?: string;
    couponName: string;
    couponType: 'percentage' | 'fixed' | 'freeShipping';
    status: 'unused' | 'used' | 'expired';
    expireTime?: string;
    updateTime?: string;
    createTime?: string;
  }
}

/**
 * 获取优惠券列表数据
 */
async function getCouponList(params: Recordable<any>) {
  return requestClient.get<Array<CouponApi.Coupon>>(
    '/coupon/list',
    { params },
  );
}

/**
 * 创建优惠券
 * @param data 优惠券数据
 */
async function createCoupon(data: Omit<CouponApi.Coupon, 'id' | 'createTime' | 'updateTime'>) {
  return requestClient.post('/coupon', data);
}

/**
 * 更新优惠券
 *
 * @param id 优惠券 ID
 * @param data 优惠券数据
 */
async function updateCoupon(
  id: string,
  data: Omit<CouponApi.Coupon, 'id' | 'createTime' | 'updateTime'>,
) {
  return requestClient.put(`/coupon/${id}`, data);
}

/**
 * 删除优惠券
 * @param id 优惠券 ID
 */
async function deleteCoupon(id: string) {
  return requestClient.delete(`/coupon/${id}`);
}

/**
 * 获取用户优惠券列表数据
 */
async function getUserCouponList(params: Recordable<any>) {
  return requestClient.get<Array<CouponApi.UserCoupon>>(
    '/user-coupon/list',
    { params },
  );
}

/**
 * 创建用户优惠券
 * @param data 用户优惠券数据
 */
async function createUserCoupon(data: Omit<CouponApi.UserCoupon, 'id' | 'createTime' | 'updateTime'>) {
  return requestClient.post('/user-coupon', data);
}

/**
 * 更新用户优惠券
 *
 * @param id 用户优惠券 ID
 * @param data 用户优惠券数据
 */
async function updateUserCoupon(
  id: string,
  data: Omit<CouponApi.UserCoupon, 'id' | 'createTime' | 'updateTime'>,
) {
  return requestClient.put(`/user-coupon/${id}`, data);
}

/**
 * 删除用户优惠券
 * @param id 用户优惠券 ID
 */
async function deleteUserCoupon(id: string) {
  return requestClient.delete(`/user-coupon/${id}`);
}

export { 
  createCoupon, 
  createUserCoupon,
  deleteCoupon, 
  deleteUserCoupon,
  getCouponList, 
  getUserCouponList,
  updateCoupon,
  updateUserCoupon
};
