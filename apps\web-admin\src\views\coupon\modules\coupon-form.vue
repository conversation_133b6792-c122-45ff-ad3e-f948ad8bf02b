<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenDrawer } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { createCoupon, updateCoupon } from '#/api';
import { $t } from '#/locales';

import { useCouponFormSchema } from '../data';

const emits = defineEmits(['success']);

const formData = ref<any>();
const id = ref();

const [Form, formApi] = useVbenForm({
  schema: useCouponFormSchema(),
  showDefaultActions: false,
});

const [Drawer, drawerApi] = useVbenDrawer({
  async onConfirm() {
    const { valid } = await formApi.validate();
    if (!valid) return;
    
    const values = await formApi.getValues();
    
    drawerApi.lock();
    try {
      if (id.value) {
        await updateCoupon(id.value, values);
        message.success(`${$t('coupon.name')}修改成功`);
      } else {
        await createCoupon(values);
        message.success(`${$t('coupon.name')}创建成功`);
      }
      emits('success');
      drawerApi.close();
    } catch (error) {
      console.error('操作失败:', error);
      message.error($t('ui.actionMessage.operationFailed'));
      drawerApi.unlock();
    }
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      const data = drawerApi.getData<any>();
      formApi.resetForm();
      if (data && Object.keys(data).length > 0) {
        formData.value = data;
        id.value = data.id;
        formApi.setValues(data);
      } else {
        formData.value = {};
        id.value = undefined;
      }
    }
  },
});

const getDrawerTitle = computed(() => {
  return formData.value?.id
    ? `修改${$t('coupon.name')}`
    : `新增${$t('coupon.name')}`;
});
</script>

<template>
  <Drawer :title="getDrawerTitle">
    <Form />
  </Drawer>
</template>
